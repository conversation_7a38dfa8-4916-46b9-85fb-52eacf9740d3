var ue=Object.defineProperty;var he=(o,n,a)=>n in o?ue(o,n,{enumerable:!0,configurable:!0,writable:!0,value:a}):o[n]=a;var V=(o,n,a)=>he(o,typeof n!="symbol"?n+"":n,a);import{r as m,a as be,R as se}from"./assets/react-vendor-CIP6LD3P.js";import{_ as R,c as ge}from"./assets/shiki-DBOBms81.js";import{c as _,C as z,P as pe,H as fe,S as ye,a as ve,D as je,A as Se,b as Ne,M as we,L as O,d as Le,e as B,X as T,f as Q,R as ae,I as Y,B as Ce,g as ke,h as W,T as Ee,i as Me,j as X,W as Pe,k as Ie}from"./assets/ui-vendor-DXj3V-Mi.js";(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))t(s);new MutationObserver(s=>{for(const r of s)if(r.type==="childList")for(const i of r.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&t(i)}).observe(document,{childList:!0,subtree:!0});function a(s){const r={};return s.integrity&&(r.integrity=s.integrity),s.referrerPolicy&&(r.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?r.credentials="include":s.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function t(s){if(s.ep)return;s.ep=!0;const r=a(s);fetch(s.href,r)}})();var re={exports:{}},D={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ae=m,Re=Symbol.for("react.element"),ze=Symbol.for("react.fragment"),Oe=Object.prototype.hasOwnProperty,Te=Ae.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,_e={key:!0,ref:!0,__self:!0,__source:!0};function oe(o,n,a){var t,s={},r=null,i=null;a!==void 0&&(r=""+a),n.key!==void 0&&(r=""+n.key),n.ref!==void 0&&(i=n.ref);for(t in n)Oe.call(n,t)&&!_e.hasOwnProperty(t)&&(s[t]=n[t]);if(o&&o.defaultProps)for(t in n=o.defaultProps,n)s[t]===void 0&&(s[t]=n[t]);return{$$typeof:Re,type:o,key:r,ref:i,props:s,_owner:Te.current}}D.Fragment=ze;D.jsx=oe;D.jsxs=oe;re.exports=D;var e=re.exports,G={},Z=be;G.createRoot=Z.createRoot,G.hydrateRoot=Z.hydrateRoot;let F=null;const M=()=>typeof window<"u"&&!!window.CSInterface,U=()=>{if(!F&&M())try{F=new window.CSInterface,console.log("CSInterface initialized successfully")}catch(o){console.error("Failed to initialize CSInterface:",o)}return F},ne=()=>{if(!M()){console.warn("Not running in CEP environment");return}const o=U();if(!o)return;o.addEventListener("com.adobe.csxs.events.ThemeColorChanged",a=>{console.log("Theme changed:",a)});const n=o.getHostEnvironment();console.log("Host environment:",n),o.evalScript("SahAI.getAppInfo()",a=>{try{if(!a||a.trim()===""){console.warn("Empty response from ExtendScript");return}const t=JSON.parse(a);console.log("ExtendScript response:",t)}catch(t){console.error("Failed to parse ExtendScript response:",t,"Raw result:",a)}})},C=(o,n=3e4,a=2)=>new Promise((t,s)=>{const r=U();if(!r){s(new Error("CSInterface not available - not running in CEP environment"));return}let i=0;const l=()=>{i++;const u=setTimeout(()=>{i<=a?(console.warn(`ExtendScript execution attempt ${i} timed out, retrying...`),l()):s(new Error(`ExtendScript execution timed out after ${n}ms (${a+1} attempts)`))},n);try{r.evalScript(o,x=>{clearTimeout(u);try{if(typeof x=="string"&&x.startsWith("EvalScript error")){if(i<=a){console.warn(`ExtendScript error on attempt ${i}, retrying...`),setTimeout(l,1e3);return}s(new Error(`ExtendScript Error: ${x}`));return}if(!x||x.trim()===""){if(i<=a){console.warn(`Empty response on attempt ${i}, retrying...`),setTimeout(l,1e3);return}s(new Error("Empty response from ExtendScript after all retries"));return}let g;try{g=JSON.parse(x)}catch{g={success:!0,data:x}}if(typeof g=="object"&&g!==null)if(g.success===!1){if(i<=a){console.warn(`ExtendScript returned failure on attempt ${i}, retrying...`),setTimeout(l,1e3);return}s(new Error(g.message||"ExtendScript execution failed"))}else t(g);else t({success:!0,data:g})}catch(g){if(i<=a){console.warn(`Error processing response on attempt ${i}, retrying...`),setTimeout(l,1e3);return}s(new Error(`Failed to process ExtendScript response: ${g}`))}})}catch(x){if(clearTimeout(u),i<=a){console.warn(`Error executing ExtendScript on attempt ${i}, retrying...`),setTimeout(l,1e3);return}s(new Error(`Failed to execute ExtendScript: ${x}`))}};l()});class k{static async save(n){const a=JSON.stringify(n);try{if(M())try{const t=await C(`saveSettings(${JSON.stringify(n)})`,1e4);if(t.success)console.log("Settings saved to CEP storage successfully");else throw new Error(t.message||"CEP save failed")}catch(t){console.warn("CEP storage save failed, falling back to localStorage:",t)}localStorage.setItem(this.SETTINGS_KEY,a),console.log("Settings saved to localStorage successfully")}catch(t){console.error("All settings save methods failed:",t);try{localStorage.setItem(this.SETTINGS_KEY,a)}catch(s){throw new Error(`Failed to save settings: ${t}. LocalStorage also failed: ${s}`)}}}static async load(){try{if(M())try{const a=await C("loadSettings()",1e4);if(a.success&&a.data)return console.log("Settings loaded from CEP storage successfully"),a.data}catch(a){console.warn("CEP storage load failed, falling back to localStorage:",a)}const n=localStorage.getItem(this.SETTINGS_KEY);if(n){const a=JSON.parse(n);return console.log("Settings loaded from localStorage successfully"),a}return console.log("No existing settings found, returning defaults"),{providers:[]}}catch(n){return console.error("All settings load methods failed:",n),{providers:[]}}}static async exportSettings(){const n=await this.load();return JSON.stringify(n,null,2)}static async importSettings(n){try{const a=JSON.parse(n);await this.save(a)}catch{throw new Error("Invalid settings format")}}static async clearSettings(){try{if(M())try{await C("saveSettings({})",1e4)}catch(n){console.warn("Failed to clear CEP storage:",n)}localStorage.removeItem(this.SETTINGS_KEY),console.log("Settings cleared successfully")}catch(n){throw new Error(`Failed to clear settings: ${n}`)}}}V(k,"SETTINGS_KEY","sahAI_settings");class K{static async checkProviderStatus(n,a){const t=Date.now();try{const{ProviderBridge:s}=await R(async()=>{const{ProviderBridge:i}=await Promise.resolve().then(()=>ie);return{ProviderBridge:i}},void 0,import.meta.url);return{isOnline:(await s.listModels(n,a.baseURL,a.apiKey)).length>0,latency:Date.now()-t}}catch(s){return{isOnline:!1,error:s.message||String(s),latency:Date.now()-t}}}}const De={async listModels(o,n,a){try{const t=`listModels('${o}', '${n||""}', '${a||""}')`,s=await C(t,15e3);if(console.log(`ProviderBridge.listModels result for ${o}:`,s),s&&typeof s=="object"){if(s.success&&s.data){let r;if(typeof s.data=="string")try{r=JSON.parse(s.data)}catch(i){return console.error("Failed to parse ExtendScript response:",i),this.getFallbackModels(o)}else r=s.data;if(r&&r.ok&&Array.isArray(r.models))return r.models.map(i=>({id:i.id,name:i.name,description:i.description||"",contextLength:i.context_length||4096,isRecommended:i.is_recommended||!1}))}if(s.ok&&Array.isArray(s.models))return s.models.map(r=>({id:r.id,name:r.name,description:r.description||"",contextLength:r.context_length||4096,isRecommended:r.is_recommended||!1}))}return console.warn(`Failed to fetch models for ${o}, using fallback`),this.getFallbackModels(o)}catch(t){return console.error(`Error fetching models for ${o}:`,t),this.getFallbackModels(o)}},getFallbackModels(o){return{openai:[{id:"gpt-4o",name:"GPT-4o",description:"Most capable OpenAI model",contextLength:128e3,isRecommended:!0},{id:"gpt-4o-mini",name:"GPT-4o Mini",description:"Faster, more affordable",contextLength:128e3,isRecommended:!1},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",description:"Legacy model",contextLength:16384,isRecommended:!1}],anthropic:[{id:"claude-3-5-sonnet-20241022",name:"Claude 3.5 Sonnet",description:"Anthropic's most capable model",contextLength:2e5,isRecommended:!0},{id:"claude-3-5-haiku-20241022",name:"Claude 3.5 Haiku",description:"Fast and efficient",contextLength:2e5,isRecommended:!1},{id:"claude-3-opus-20240229",name:"Claude 3 Opus",description:"Powerful reasoning",contextLength:2e5,isRecommended:!1}],gemini:[{id:"gemini-1.5-pro",name:"Gemini 1.5 Pro",description:"Google's most capable model",contextLength:2e6,isRecommended:!0},{id:"gemini-1.5-flash",name:"Gemini 1.5 Flash",description:"Fast and efficient",contextLength:1e6,isRecommended:!1}],groq:[{id:"llama-3.1-8b-instant",name:"Llama 3.1 8B",description:"Fast inference",contextLength:131072,isRecommended:!1},{id:"llama-3.1-70b-versatile",name:"Llama 3.1 70B",description:"Balanced performance",contextLength:131072,isRecommended:!0},{id:"mixtral-8x7b-32768",name:"Mixtral 8x7B",description:"Large context",contextLength:32768,isRecommended:!1}],deepseek:[{id:"deepseek-chat",name:"DeepSeek Chat",description:"General purpose",contextLength:128e3,isRecommended:!0},{id:"deepseek-coder",name:"DeepSeek Coder",description:"Code-focused",contextLength:128e3,isRecommended:!1}],mistral:[{id:"mistral-large-latest",name:"Mistral Large",description:"Most capable",contextLength:128e3,isRecommended:!0},{id:"mistral-medium-latest",name:"Mistral Medium",description:"Balanced",contextLength:32e3,isRecommended:!1},{id:"mistral-small-latest",name:"Mistral Small",description:"Fast and efficient",contextLength:32e3,isRecommended:!1}],moonshot:[{id:"moonshot-v1-128k",name:"Moonshot v1 128K",description:"Large context",contextLength:128e3,isRecommended:!0},{id:"moonshot-v1-32k",name:"Moonshot v1 32K",description:"Medium context",contextLength:32e3,isRecommended:!1}],openrouter:[{id:"openai/gpt-4o",name:"GPT-4o (OpenRouter)",description:"OpenAI via OpenRouter",contextLength:128e3,isRecommended:!0},{id:"anthropic/claude-3.5-sonnet",name:"Claude 3.5 Sonnet (OpenRouter)",description:"Anthropic via OpenRouter",contextLength:2e5,isRecommended:!1}],perplexity:[{id:"llama-3.1-sonar-large-128k-online",name:"Llama 3.1 Sonar Large 128K Online",description:"Large online model",contextLength:128e3,isRecommended:!0},{id:"llama-3.1-sonar-small-128k-online",name:"Llama 3.1 Sonar Small 128K Online",description:"Small online model",contextLength:128e3,isRecommended:!1}],qwen:[{id:"qwen-max",name:"Qwen Max",description:"Most capable",contextLength:32e3,isRecommended:!0},{id:"qwen-plus",name:"Qwen Plus",description:"Balanced performance",contextLength:32e3,isRecommended:!1},{id:"qwen-turbo",name:"Qwen Turbo",description:"Fast and efficient",contextLength:8e3,isRecommended:!1}],together:[{id:"meta-llama/Llama-3-70b-chat-hf",name:"Llama 3 70B Chat",description:"Large language model",contextLength:8192,isRecommended:!0},{id:"meta-llama/Llama-3-8b-chat-hf",name:"Llama 3 8B Chat",description:"Smaller, faster model",contextLength:8192,isRecommended:!1}],vertex:[{id:"gemini-1.5-pro",name:"Gemini 1.5 Pro",description:"Google's most capable model",contextLength:2e6,isRecommended:!0},{id:"gemini-1.5-flash",name:"Gemini 1.5 Flash",description:"Fast and efficient",contextLength:1e6,isRecommended:!1}],xai:[{id:"grok-beta",name:"Grok Beta",description:"xAI's flagship model",contextLength:128e3,isRecommended:!0},{id:"grok-vision-beta",name:"Grok Vision Beta",description:"Vision-capable model",contextLength:128e3,isRecommended:!1}],ollama:[{id:"llama3.1",name:"Llama 3.1",description:"Open source LLM",contextLength:4096,isRecommended:!0},{id:"mistral",name:"Mistral",description:"Efficient transformer",contextLength:8192,isRecommended:!1},{id:"codellama",name:"Code Llama",description:"Code-focused",contextLength:16384,isRecommended:!1}],lmstudio:[{id:"local-model",name:"Local Model",description:"Your local model",contextLength:4096,isRecommended:!0}]}[o]||[]}},ie=Object.freeze(Object.defineProperty({__proto__:null,CEPSettings:k,ProviderBridge:De,ProviderStatusChecker:K,executeExtendScript:C,getCSInterface:U,initializeCEP:ne,isCEPEnvironment:M},Symbol.toStringTag,{value:"Module"})),A=_((o,n)=>({providers:[{id:"openai",name:"OpenAI",isConfigured:!1,models:[]},{id:"anthropic",name:"Anthropic",isConfigured:!1,models:[]},{id:"gemini",name:"Google Gemini",isConfigured:!1,models:[]},{id:"groq",name:"Groq",isConfigured:!1,models:[]},{id:"deepseek",name:"DeepSeek",isConfigured:!1,models:[]},{id:"mistral",name:"Mistral",isConfigured:!1,models:[]},{id:"moonshot",name:"Moonshot AI",isConfigured:!1,models:[]},{id:"openrouter",name:"OpenRouter",isConfigured:!1,models:[]},{id:"perplexity",name:"Perplexity",isConfigured:!1,models:[]},{id:"qwen",name:"Alibaba Qwen",isConfigured:!1,models:[]},{id:"together",name:"Together AI",isConfigured:!1,models:[]},{id:"vertex",name:"Google Vertex AI",isConfigured:!1,models:[]},{id:"xai",name:"xAI",isConfigured:!1,models:[]},{id:"ollama",name:"Ollama",isConfigured:!1,models:[]},{id:"lmstudio",name:"LM Studio",isConfigured:!1,models:[]}],activeProviderId:void 0,isLoadingModels:!1,setActiveProvider:a=>{o({activeProviderId:a}),n().persistSettings()},updateProviderConfig:(a,t)=>{o(s=>({providers:s.providers.map(r=>r.id===a?{...r,...t,isConfigured:!!t.apiKey}:r)})),n().persistSettings()},setProviderModels:(a,t)=>{o(s=>({providers:s.providers.map(r=>r.id===a?{...r,models:t,isLoading:!1,error:void 0}:r)}))},setSelectedModel:(a,t)=>{o(s=>({providers:s.providers.map(r=>r.id===a?{...r,selectedModelId:t}:r)})),n().persistSettings()},updateProviderKey:(a,t,s)=>{o(r=>({providers:r.providers.map(i=>i.id===a?{...i,apiKey:t,isConfigured:!!t,selectedModelId:s||i.selectedModelId}:i)})),n().persistSettings()},saveProviderSelection:(a,t)=>{o(s=>({activeProviderId:a,providers:s.providers.map(r=>r.id===a?{...r,...t,isConfigured:!!(t.apiKey||r.baseURL)}:r)})),n().persistSettings()},loadModelsForProvider:async a=>{const t=n().providers.find(s=>s.id===a);if(!(t!=null&&t.isConfigured)){console.warn(`Provider ${a} is not configured, skipping model loading`);return}console.log(`Loading models for provider: ${a}`),o(s=>({providers:s.providers.map(r=>r.id===a?{...r,isLoading:!0,error:void 0}:r)}));try{const{ProviderBridge:s}=await R(async()=>{const{ProviderBridge:l}=await Promise.resolve().then(()=>ie);return{ProviderBridge:l}},void 0,import.meta.url),r=await s.listModels(a,t.baseURL,t.apiKey);console.log(`Received ${r.length} models for ${a}:`,r);const i=r.map(l=>({id:l.id,name:l.name,description:l.description||"",contextLength:l.contextLength||4096,isRecommended:l.isRecommended||!1}));console.log(`Transformed models for ${a}:`,i),n().setProviderModels(a,i)}catch(s){console.error(`Error loading models for ${a}:`,s);const r=(s==null?void 0:s.message)||String(s),i=r.includes("timeout")?"Request timed out. Please check your internet connection and try again.":r.includes("network")?"Network error. Please check your internet connection.":r.includes("unauthorized")||r.includes("401")?"Invalid API key. Please check your credentials.":r.includes("forbidden")||r.includes("403")?"Access denied. Please check your API key permissions.":r.includes("not found")||r.includes("404")?"API endpoint not found. Please check the provider configuration.":r;o(l=>({providers:l.providers.map(u=>u.id===a?{...u,isLoading:!1,error:i}:u)}))}},persistSettings:()=>{const{activeProviderId:a,providers:t}=n();k.save({activeProviderId:a,providers:t.map(s=>({id:s.id,isConfigured:s.isConfigured,apiKey:s.apiKey,baseURL:s.baseURL,selectedModelId:s.selectedModelId,settings:s.settings}))})},loadSettings:async()=>{try{const a=await k.load();a.activeProviderId&&o({activeProviderId:a.activeProviderId}),a.providers&&Array.isArray(a.providers)&&o(t=>({providers:t.providers.map(s=>{var i;const r=(i=a.providers)==null?void 0:i.find(l=>l.id===s.id);return r?{...s,...r}:s})}))}catch(a){console.error("Failed to load CEP settings:",a)}},getActiveProvider:()=>{const{providers:a,activeProviderId:t}=n();return a.find(s=>s.id===t)||null},getActiveModel:()=>{const a=n().getActiveProvider();return a!=null&&a.selectedModelId&&a.models.find(t=>t.id===a.selectedModelId)||null}})),P=_(o=>({modal:null,openModal:n=>o({modal:n}),closeModal:()=>o({modal:null})})),J=_((o,n)=>({messages:[],isLoading:!1,addMessage:a=>{const t={...a,id:crypto.randomUUID(),timestamp:Date.now()};o(r=>({messages:[...r.messages,t]}));const s=n().currentSession;s&&R(async()=>{const{useHistoryStore:r}=await Promise.resolve().then(()=>te);return{useHistoryStore:r}},void 0,import.meta.url).then(({useHistoryStore:r})=>{var u;const i=r.getState(),l=i.sessions.find(x=>x.id===s);if(l){const x={...l,messages:[...n().messages],updatedAt:Date.now(),title:l.title===`Chat ${new Date(l.createdAt).toLocaleDateString()}`&&((u=n().messages[0])==null?void 0:u.content.slice(0,50))+"..."||l.title};i.saveSession(x)}})},setLoading:a=>o({isLoading:a}),createNewSession:()=>{const a=crypto.randomUUID();return o({messages:[],currentSession:a}),R(async()=>{const{useHistoryStore:t}=await Promise.resolve().then(()=>te);return{useHistoryStore:t}},void 0,import.meta.url).then(({useHistoryStore:t})=>{t.getState().createSession()}),a},loadSession:(a,t)=>{o({currentSession:a,messages:t})},clearMessages:()=>{o({messages:[],currentSession:void 0})}})),$e=()=>{const{getActiveProvider:o}=A(),[n,a]=m.useState({isOnline:null,isChecking:!1}),t=o();m.useEffect(()=>{let r;const i=async()=>{if(!(t!=null&&t.isConfigured)){a({isOnline:null,isChecking:!1});return}a(l=>({...l,isChecking:!0,error:void 0}));try{const l=await K.checkProviderStatus(t.id,{apiKey:t.apiKey,baseURL:t.baseURL});a({isOnline:l.isOnline,latency:l.latency,isChecking:!1})}catch(l){a({isOnline:!1,isChecking:!1,error:l.message})}};return t!=null&&t.isConfigured&&(i(),r=setInterval(i,3e4)),()=>{r&&clearInterval(r)}},[t]);const s=()=>{const r="w-2.5 h-2.5 rounded-full transition-all duration-300 shadow-sm hover:scale-110 cursor-pointer";return n.isChecking?`${r} bg-adobe-warning animate-pulse shadow-adobe-warning/40`:n.isOnline===!0?`${r} bg-adobe-success shadow-adobe-success/40`:`${r} bg-adobe-error shadow-adobe-error/40`};return e.jsx("div",{className:s()})},Fe=()=>{const{getActiveProvider:o,getActiveModel:n,loadSettings:a}=A(),{openModal:t}=P(),{createNewSession:s}=J(),r=o(),i=n(),l=m.useMemo(()=>r?i?`${r.name} • ${i.name}`:`${r.name} • Select Model`:"Select AI Provider & Model",[r,i]);return m.useEffect(()=>{a()},[a]),e.jsxs("header",{className:"flex items-center justify-between px-4 py-3 border-b border-adobe-border bg-adobe-bg-secondary shadow-sm",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("button",{onClick:()=>t("status"),className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all flex items-center justify-center",children:e.jsx($e,{})}),e.jsxs("button",{onClick:()=>t("provider"),className:"flex items-center space-x-2 text-sm font-medium text-adobe-text-primary hover:text-adobe-accent transition-colors group",title:"Select AI Provider & Model",children:[e.jsx("span",{className:"max-w-[300px] truncate",children:l}),e.jsx(z,{size:14,className:"text-adobe-text-secondary group-hover:text-adobe-accent transition-colors"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{onClick:s,className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all",title:"New Chat",children:e.jsx(pe,{size:16})}),e.jsx("div",{className:"h-4 w-px bg-adobe-border"}),e.jsx("button",{onClick:()=>t("chat-history"),className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all",title:"Chat History",children:e.jsx(fe,{size:16})}),e.jsx("button",{onClick:()=>t("settings"),className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all",title:"Settings",children:e.jsx(ye,{size:16})})]})]})},He=["javascript","typescript","jsx","tsx","html","css","scss","less","json","jsonc","xml","yaml","markdown","python","swift","rust","go","java","php","ruby","shell","actionscript-3"],Be=["github-dark"],Ge=ge({themes:Be,langs:He});let H=null;async function Ue(){return H||(H=await Ge),H}function de(o){return["javascript","typescript","jsx","tsx","html","css","scss","less","json","jsonc","xml","yaml","markdown","python","swift","rust","go","java","php","ruby","shell","actionscript-3","actionscript"].includes(o.toLowerCase())}function Ke(o){const n={js:"javascript",ts:"typescript",bash:"shell",sh:"shell",zsh:"shell",fish:"shell",py:"python",rb:"ruby",yml:"yaml",htm:"html",sass:"scss",jsx:"jsx",tsx:"tsx"},a=o.toLowerCase();return n[a]?n[a]:de(a)?a:"text"}const Je=({content:o})=>{const[n,a]=m.useState(null);if(m.useEffect(()=>{Ue().then(a)},[]),!n)return e.jsx("pre",{className:"whitespace-pre-wrap",children:o});const t=o.split(/(```[\s\S]*?```)/g);return e.jsx(e.Fragment,{children:t.map((s,r)=>{if(s.startsWith("```")){const i=s.split(`
`),l=i[0].replace("```","").trim(),u=i.slice(1,-1).join(`
`),x=de(l)?l:Ke(l);return e.jsxs("div",{className:"relative",children:[e.jsxs("div",{className:"absolute top-1 right-1 flex gap-1",children:[e.jsx("button",{title:"Copy",onClick:()=>navigator.clipboard.writeText(u),className:"p-1 bg-adobe-bg-primary/80 rounded text-xs",children:e.jsx(ve,{size:14})}),e.jsx("button",{title:"Save",className:"p-1 bg-adobe-bg-primary/80 rounded text-xs",children:e.jsx(je,{size:14})})]}),e.jsx("div",{dangerouslySetInnerHTML:{__html:n.codeToHtml(u,{lang:x,theme:"github-dark"})}})]},r)}return e.jsx("div",{children:s},r)})})},qe=({message:o})=>{const n=o.role==="user";return e.jsx("div",{className:`flex gap-3 ${n?"justify-end":"justify-start"} mb-4`,children:e.jsx("div",{className:`max-w-[85%] rounded-2xl px-4 py-3 text-sm leading-relaxed shadow-sm ${n?"bg-adobe-bg-tertiary text-adobe-text-primary ml-8 rounded-br-md":"bg-adobe-bg-primary text-adobe-text-primary mr-8 rounded-bl-md border border-adobe-border"}`,children:e.jsx("div",{className:"whitespace-pre-wrap",children:e.jsx(Je,{content:o.content})})})})},Ve=""+new URL("assets/BrandLogo-BoAYSo97.svg",import.meta.url).href,Qe=()=>{const{messages:o,isLoading:n,currentSession:a}=J(),t=m.useRef(null),s=m.useRef(null),[r,i]=m.useState(!1),l=m.useRef();m.useEffect(()=>{var x;(x=t.current)==null||x.scrollIntoView({behavior:"smooth"})},[o,n]),m.useEffect(()=>{const x=s.current;if(!x)return;const g=()=>{clearTimeout(l.current);const{scrollTop:y,scrollHeight:p,clientHeight:v}=x,N=p-(y+v)<100;i(!N),l.current=setTimeout(()=>{i(!1)},2e3)};return x.addEventListener("scroll",g),()=>{x.removeEventListener("scroll",g),clearTimeout(l.current)}},[]);const u=()=>{var x;(x=t.current)==null||x.scrollIntoView({behavior:"smooth"})};return e.jsxs("div",{ref:s,className:`flex-1 overflow-y-auto px-3 py-2 space-y-4
                chat-messages-scrollbar
                relative`,children:[(!a||o.length===0)&&e.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary gap-3",children:[e.jsx("div",{className:"w-20 h-20 mb-2 flex items-center justify-center",children:e.jsx("img",{src:Ve,alt:"SahAI Logo",className:"w-20 h-20 brightness-0 invert"})}),e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:"Start a conversation"}),e.jsx("p",{className:"text-sm text-center max-w-md",children:"Type a message below to begin chatting with SahAI"})]}),o.map(x=>e.jsx(qe,{message:x},x.id)),n&&e.jsx("div",{className:"flex items-center gap-2 text-adobe-text-secondary text-sm",children:e.jsx("span",{children:"AI is thinking..."})}),e.jsx("div",{ref:t}),r&&e.jsx("button",{onClick:u,className:"absolute right-4 bottom-4 p-2 rounded-full bg-adobe-bg-tertiary border border-adobe-border text-adobe-text-primary hover:bg-adobe-bg-secondary transition-all duration-300 shadow-md","aria-label":"Scroll to bottom",children:e.jsx(Se,{size:18})})]})},Ye=se.memo(({onAttachFile:o,onVoiceInput:n})=>{const[a,t]=m.useState(""),[s,r]=m.useState(!1),i=m.useRef(null),{addMessage:l,isLoading:u,setLoading:x,currentSession:g,createNewSession:y}=J(),p=4e3,v=!a.trim(),N=a.length>p*.9;m.useEffect(()=>{const d=i.current;d&&d.style.setProperty("--textarea-height","72px")},[]);const L=m.useCallback(()=>{const d=i.current;if(!d)return;d.style.height="auto";const c=Math.min(Math.max(d.scrollHeight,72),200);d.style.setProperty("--textarea-height",`${c}px`),d.style.height=""},[]),j=m.useCallback(d=>{t(d.target.value),L()},[L]),S=m.useCallback(async()=>{const d=a.trim();if(!(!d||u)){t(""),i.current&&i.current.style.setProperty("--textarea-height","72px");try{x(!0),g||y(),l({content:d,role:"user"}),setTimeout(()=>{l({content:`Echo: ${d}`,role:"assistant"}),x(!1)},1e3)}catch{t(d),x(!1)}}},[a,u,g,l,x,y]),h=m.useCallback(d=>{d.key==="Enter"&&!d.shiftKey&&!s&&(d.preventDefault(),S())},[S,s]);return e.jsxs("div",{className:"px-4 pb-3 pt-2 bg-adobe-bg-secondary border-t border-adobe-border",children:[e.jsxs("div",{className:"relative flex items-center bg-transparent rounded-lg border border-adobe-text-secondary/50 focus-within:border-adobe-accent focus-within:ring-1 focus-within:ring-adobe-accent transition-colors",children:[e.jsx("div",{className:"flex items-center pl-3",children:e.jsx("button",{onClick:o,className:"text-adobe-text-secondary hover:text-adobe-accent transition p-1.5 rounded",title:"Attach file",disabled:u,children:e.jsx(Ne,{size:18})})}),e.jsx("textarea",{ref:i,rows:3,maxLength:p,value:a,onChange:j,onKeyDown:h,onCompositionStart:()=>r(!0),onCompositionEnd:()=>r(!1),placeholder:"Type a message...",className:`flex-1 resize-none bg-transparent text-adobe-text-primary text-sm p-3 outline-none placeholder:text-adobe-text-secondary/80
            auto-resize-textarea leading-relaxed overflow-y-auto chat-messages-scrollbar`}),e.jsxs("div",{className:"flex items-center pr-3 space-x-1",children:[e.jsx("button",{onClick:n,className:"text-adobe-text-secondary hover:text-adobe-warning transition p-1.5 rounded disabled:opacity-40",title:"Voice input",disabled:u,children:e.jsx(we,{size:18})}),e.jsx("button",{onClick:S,disabled:v||u,className:"text-adobe-accent hover:text-adobe-accent-hover transition p-1.5 rounded disabled:text-adobe-text-secondary/50 disabled:hover:text-adobe-text-secondary/50",title:"Send",children:u?e.jsx(O,{size:18,className:"animate-spin"}):e.jsx(Le,{size:18})})]})]}),e.jsxs("div",{className:"flex justify-between items-center mt-1 px-1",children:[e.jsxs("span",{className:`text-xs ${N?"text-adobe-warning":"text-adobe-text-secondary"}`,children:[a.length,"/",p]}),e.jsx("span",{className:"text-xs text-adobe-text-secondary",children:"Enter to send, Shift+Enter for new line"})]})]})}),We=({provider:o,size:n=16,className:a="",...t})=>{const s={width:n,height:n,viewBox:"0 0 24 24",fill:"currentColor",className:`provider-logo ${a}`,...t};switch(o){case"openai":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142-.0852 4.783-2.7582a.7712.7712 0 0 0 .7806 0l5.8428 3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zm-2.4569-16.2971a4.4755 4.4755 0 0 1 2.3445-1.9275L5.943 7.1778a.7663.7663 0 0 0 .3717.6388l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0L4.2446 9.8211a4.504 4.504 0 0 1-.7876-8.4285zm16.5618 3.8558l-5.8428-3.3685V4.4444a.0804.0804 0 0 1 .0332-.0615l4.8645-2.8077a4.4992 4.4992 0 0 1 6.6802 4.66l-.1465.0804-4.7806 2.7582a.7712.7712 0 0 0-.7806 0zm2.0107-3.0231l-.142.0852-4.7806 2.7582a.7663.7663 0 0 0-.3717.6388L9.74 4.1818l2.0201-1.1686a.0757.0757 0 0 1 .071 0l4.8076 2.7748a4.504 4.504 0 0 1 .7876 8.4285z"})});case"anthropic":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2L2 22h4l2-5h8l2 5h4L12 2zm0 6l2.5 6h-5L12 8z"})});case"gemini":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})});case"groq":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})});case"deepseek":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2l10 18H2L12 2zm0 3.5L5.5 18h13L12 5.5z"})});case"mistral":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2L2 7v10l10 5 10-5V7l-10-5zm0 2.18L19.82 8 12 11.82 4.18 8 12 4.18zM4 9.48l7 3.5v7.84l-7-3.5V9.48zm16 0v7.84l-7 3.5v-7.84l7-3.5z"})});case"moonshot":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 1L9 9l-8 3 8 3 3 8 3-8 8-3-8-3-3-8z"})});case"openrouter":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2L2 12l10 10 10-10L12 2zm0 3.41L18.59 12 12 18.59 5.41 12 12 5.41z"})});case"perplexity":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6H9l3-3 3 3h-2v6z"})});case"qwen":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})});case"together":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zM6 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm6 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm-6 0c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2z"})});case"vertex":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2L2 7v10l10 5 10-5V7l-10-5zm0 2.18L19.82 8 12 11.82 4.18 8 12 4.18zM4 9.48l7 3.5v7.84l-7-3.5V9.48zm16 0v7.84l-7 3.5v-7.84l7-3.5z"})});case"xai":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M18.36 5.64L12 12l6.36 6.36-1.41 1.41L12 14.83l-4.95 4.94-1.41-1.41L12 12 5.64 5.64l1.41-1.41L12 9.17l4.95-4.94 1.41 1.41z"})});case"ollama":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"})});case"lmstudio":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"})});default:return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})})}},ee=[{value:"openai",label:"OpenAI"},{value:"anthropic",label:"Anthropic"},{value:"gemini",label:"Google Gemini"},{value:"groq",label:"Groq"},{value:"deepseek",label:"DeepSeek"},{value:"mistral",label:"Mistral"},{value:"moonshot",label:"Moonshot AI"},{value:"openrouter",label:"OpenRouter"},{value:"perplexity",label:"Perplexity"},{value:"qwen",label:"Alibaba Qwen"},{value:"together",label:"Together AI"},{value:"vertex",label:"Google Vertex AI"},{value:"xai",label:"xAI"},{value:"ollama",label:"Ollama"},{value:"lmstudio",label:"LM Studio"}],Xe=()=>{const{closeModal:o}=P(),{providers:n,saveProviderSelection:a,loadModelsForProvider:t}=A(),[s,r]=m.useState(""),[i,l]=m.useState(""),[u,x]=m.useState(""),[g,y]=m.useState(""),[p,v]=m.useState(""),[N,L]=m.useState(!1),[j,S]=m.useState(!1),h=m.useRef(null),d=m.useRef(null),c=n.find(b=>b.id===s),f=(c==null?void 0:c.models)||[],w=ee.filter(b=>b.label.toLowerCase().includes(g.toLowerCase())),le=f.filter(b=>b.name.toLowerCase().includes(p.toLowerCase()));m.useEffect(()=>{const b=E=>{h.current&&!h.current.contains(E.target)&&L(!1),d.current&&!d.current.contains(E.target)&&S(!1)};return document.addEventListener("mousedown",b),()=>document.removeEventListener("mousedown",b)},[]),m.useEffect(()=>{if(!s||!u.trim())return;const b=setTimeout(()=>{a(s,{apiKey:u}),t(s)},300);return()=>clearTimeout(b)},[s,u,a,t]);const ce=m.useCallback(b=>{var I;const E=((I=ee.find($=>$.value===b))==null?void 0:I.label)||"";r(b),y(E),l(""),v(""),L(!1)},[]),me=m.useCallback(b=>{var I;const E=((I=f.find($=>$.id===b))==null?void 0:I.name)||"";l(b),v(E),S(!1)},[f]),xe=m.useCallback(()=>{!s||!i||!u.trim()||(a(s,{apiKey:u,selectedModelId:i}),o())},[s,i,u,a,o]);return e.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:e.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[500px] shadow-2xl",children:[e.jsxs("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4 flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"AI Provider Configuration"}),e.jsx("button",{onClick:o,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:"✕"})]}),e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsxs("div",{className:"relative",ref:h,children:[e.jsx("label",{className:"block text-sm font-medium text-adobe-text-primary mb-2",children:"Provider"}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",value:g,onChange:b=>{y(b.target.value),L(!0)},onFocus:()=>L(!0),placeholder:"Search providers...",className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none pr-10"}),e.jsx(B,{className:"absolute right-3 top-1/2 -translate-y-1/2 text-adobe-text-secondary",size:18})]}),N&&e.jsx("div",{className:"absolute z-20 mt-2 w-full bg-adobe-bg-secondary border border-adobe-border rounded-md shadow-xl max-h-60 overflow-auto",children:w.map(b=>e.jsxs("div",{className:"px-4 py-3 cursor-pointer flex items-center space-x-3 hover:bg-adobe-bg-tertiary",onClick:()=>ce(b.value),children:[e.jsx(We,{provider:b.value,size:16}),e.jsx("span",{className:"text-adobe-text-primary font-medium",children:b.label})]},b.value))})]}),e.jsxs("div",{className:"relative",ref:d,children:[e.jsx("label",{className:"block text-sm font-medium text-adobe-text-primary mb-2",children:"Model"}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",value:p,onChange:b=>{v(b.target.value),S(!0)},onFocus:()=>s&&S(!0),placeholder:s?"Search models...":"Select a provider first",disabled:!s,className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none pr-10 disabled:opacity-50"}),e.jsx(B,{className:"absolute right-3 top-1/2 -translate-y-1/2 text-adobe-text-secondary",size:18}),(c==null?void 0:c.isLoading)&&e.jsx(O,{className:"absolute right-10 top-1/2 -translate-y-1/2 text-adobe-text-secondary animate-spin",size:16})]}),j&&s&&e.jsxs("div",{className:"absolute z-20 mt-2 w-full bg-adobe-bg-secondary border border-adobe-border rounded-md shadow-xl max-h-60 overflow-auto",children:[(c==null?void 0:c.isLoading)&&f.length===0&&e.jsxs("div",{className:"px-4 py-3 flex items-center space-x-2 text-adobe-text-secondary",children:[e.jsx(O,{size:16,className:"animate-spin"}),e.jsx("span",{children:"Loading models..."})]}),(c==null?void 0:c.error)&&f.length===0&&e.jsxs("div",{className:"px-4 py-3",children:[e.jsx("div",{className:"text-red-400 text-sm font-medium",children:"Failed to load models"}),e.jsx("div",{className:"text-adobe-text-secondary text-xs mt-1",children:c.error}),e.jsx("button",{onClick:()=>t(s),className:"text-adobe-accent text-xs mt-2 hover:underline",children:"Retry"})]}),le.map(b=>e.jsxs("div",{className:"px-4 py-3 cursor-pointer hover:bg-adobe-bg-tertiary",onClick:()=>me(b.id),children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-adobe-text-primary font-medium",children:b.name}),b.isRecommended&&e.jsx("span",{className:"text-xs bg-adobe-accent/20 text-adobe-accent px-2 py-1 rounded",children:"Recommended"})]}),b.description&&e.jsx("p",{className:"text-xs text-adobe-text-secondary mt-1",children:b.description})]},b.id))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-adobe-text-primary mb-2",children:"API Key"}),e.jsx("input",{type:"password",value:u,onChange:b=>x(b.target.value),placeholder:"Enter your API key",className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none"})]})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary border-t border-adobe-border p-4 flex justify-between",children:[e.jsx("button",{onClick:o,className:"px-4 py-2 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:"Cancel"}),e.jsx("button",{onClick:xe,disabled:!s||!i||!u.trim(),className:"px-6 py-2 bg-adobe-accent text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-adobe-accent/90 transition-colors",children:"Save & Close"})]})]})})},q=_((o,n)=>({sessions:[],currentSessionId:null,isLoading:!1,error:null,loadHistory:async()=>{o({isLoading:!0,error:null});try{if(!window.CSInterface){const s=localStorage.getItem("sahai-chat-history"),r=s?JSON.parse(s):[];o({sessions:r,isLoading:!1});return}const t=await C("loadHistory()");if(t&&t.success&&t.data){const s=Array.isArray(t.data)?t.data:[];o({sessions:s,isLoading:!1})}else if(t&&t.success)o({sessions:[],isLoading:!1});else throw new Error((t==null?void 0:t.message)||"Failed to load history from ExtendScript")}catch(a){console.error("Failed to load history:",a);try{const t=localStorage.getItem("sahai-chat-history"),s=t?JSON.parse(t):[];o({sessions:s,isLoading:!1,error:`Using local storage fallback: ${a.message}`})}catch{o({error:a.message||"Failed to load chat history",isLoading:!1,sessions:[]})}}},saveSession:async a=>{try{o(r=>({sessions:r.sessions.some(i=>i.id===a.id)?r.sessions.map(i=>i.id===a.id?a:i):[...r.sessions,a]}));const t=n().sessions;if(!window.CSInterface){localStorage.setItem("sahai-chat-history",JSON.stringify(t));return}await C(`saveHistory(${JSON.stringify(t)})`),localStorage.setItem("sahai-chat-history",JSON.stringify(t))}catch(t){console.error("Failed to save session:",t);try{const s=n().sessions;localStorage.setItem("sahai-chat-history",JSON.stringify(s))}catch{o({error:t.message||"Failed to save session"})}}},deleteSession:async a=>{try{o(r=>({sessions:r.sessions.filter(i=>i.id!==a),currentSessionId:r.currentSessionId===a?null:r.currentSessionId}));const t=n().sessions;if(!window.CSInterface){localStorage.setItem("sahai-chat-history",JSON.stringify(t));return}await C(`saveHistory(${JSON.stringify(t)})`),localStorage.setItem("sahai-chat-history",JSON.stringify(t))}catch(t){console.error("Failed to delete session:",t);try{const s=n().sessions;localStorage.setItem("sahai-chat-history",JSON.stringify(s))}catch{o({error:t.message||"Failed to delete session"})}}},clearHistory:async()=>{try{if(o({sessions:[],currentSessionId:null}),!window.CSInterface){localStorage.setItem("sahai-chat-history",JSON.stringify([]));return}await C("saveHistory([])"),localStorage.setItem("sahai-chat-history",JSON.stringify([]))}catch(a){console.error("Failed to clear history:",a);try{localStorage.setItem("sahai-chat-history",JSON.stringify([]))}catch{o({error:a.message||"Failed to clear history"})}}},createSession:a=>{const t={id:crypto.randomUUID(),title:a||`Chat ${new Date().toLocaleDateString()}`,messages:[],createdAt:Date.now(),updatedAt:Date.now()};o(r=>({sessions:[t,...r.sessions],currentSessionId:t.id}));const{saveSession:s}=n();return s(t),t},updateSession:(a,t)=>{o(s=>({sessions:s.sessions.map(r=>r.id===a?{...r,...t,updatedAt:Date.now()}:r)}))},setCurrentSession:a=>{o({currentSessionId:a})},getCurrentSession:()=>{const{sessions:a,currentSessionId:t}=n();return a.find(s=>s.id===t)||null},getSessionById:a=>{const{sessions:t}=n();return t.find(s=>s.id===a)||null},getSortedSessions:()=>{const{sessions:a}=n();return[...a].sort((t,s)=>s.updatedAt-t.updatedAt)}})),te=Object.freeze(Object.defineProperty({__proto__:null,useHistoryStore:q},Symbol.toStringTag,{value:"Module"})),Ze=()=>{const{closeModal:o}=P(),{sessions:n}=q(),[a,t]=m.useState({theme:"auto",autoSave:!0,showNotifications:!0,maxHistoryItems:100,debugMode:!1}),[s,r]=m.useState("settings"),[i,l]=m.useState(!1),[u,x]=m.useState(!0),[g,y]=m.useState("30d"),[p,v]=m.useState(!1);m.useEffect(()=>{(async()=>{try{const c=await k.load();c.appSettings&&t(c.appSettings)}catch(c){console.error("Failed to load settings:",c)}finally{x(!1)}})()},[]);const N=[{id:"settings",title:"General Settings",description:"Configure application preferences",icon:e.jsx(Q,{size:16,className:"text-adobe-accent"})},{id:"analytics",title:"Analytics",description:"View usage statistics",icon:e.jsx(Ce,{size:16,className:"text-adobe-accent"})},{id:"help",title:"Help & Support",description:"Get help and answers",icon:e.jsx(ke,{size:16,className:"text-adobe-accent"})},{id:"about",title:"About",description:"About SahAI Extension",icon:e.jsx(Y,{size:16,className:"text-adobe-accent"})}],j=(()=>{const d=Date.now(),c=n.filter(f=>{if(g==="all")return!0;const w=parseInt(g.replace("d",""));return d-f.createdAt<=w*24*60*60*1e3});return{messages:c.reduce((f,w)=>f+w.messages.length,0),sessions:c.length,tokens:c.reduce((f,w)=>f+(w.tokenCount||0),0),cost:c.reduce((f,w)=>f+(w.cost||0),0),avgLatency:c.length>0?c.reduce((f,w)=>f+(w.avgLatency||0),0)/c.length:0}})(),S=()=>{v(!0),setTimeout(()=>v(!1),800)},h=async()=>{l(!0);try{const c={...await k.load(),appSettings:a};await k.save(c),o()}catch(d){console.error("Failed to save settings:",d)}finally{l(!1)}};return e.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:e.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col",children:[e.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"Settings"}),e.jsx("button",{onClick:o,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(T,{size:20})})]})}),e.jsxs("div",{className:"flex-1 flex overflow-hidden",children:[e.jsx("div",{className:"w-1/3 border-r border-adobe-border bg-adobe-bg-secondary p-4 overflow-y-auto",children:e.jsx("div",{className:"space-y-1",children:N.map(d=>e.jsx("button",{onClick:()=>r(d.id),className:`w-full text-left p-3 rounded-md transition-colors ${s===d.id?"bg-adobe-accent/10 text-adobe-text-primary":"text-adobe-text-secondary hover:bg-adobe-bg-tertiary"}`,children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-1.5 rounded-md bg-adobe-bg-tertiary",children:d.icon}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-sm",children:d.title}),e.jsx("div",{className:"text-xs mt-1",children:d.description})]})]})},d.id))})}),e.jsx("div",{className:"w-2/3 p-6 overflow-y-auto",children:u?e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-adobe-accent"})}):s==="settings"?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"Appearance"}),e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-adobe-text-secondary mb-2",children:"Theme"}),e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:a.theme,onChange:d=>t(c=>({...c,theme:d.target.value})),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pr-8 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none appearance-none",children:[e.jsx("option",{value:"auto",children:"Auto (System)"}),e.jsx("option",{value:"light",children:"Light"}),e.jsx("option",{value:"dark",children:"Dark"})]}),e.jsx(z,{size:16,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary pointer-events-none"})]})]})})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"General"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("label",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"checkbox",checked:a.autoSave,onChange:d=>t(c=>({...c,autoSave:d.target.checked})),className:"w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"}),e.jsx("span",{className:"text-sm text-adobe-text-primary",children:"Auto-save conversations"})]}),e.jsxs("label",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"checkbox",checked:a.showNotifications,onChange:d=>t(c=>({...c,showNotifications:d.target.checked})),className:"w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"}),e.jsx("span",{className:"text-sm text-adobe-text-primary",children:"Show notifications"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm text-adobe-text-secondary mb-2",children:["Max history items (",a.maxHistoryItems,")"]}),e.jsx("input",{type:"range",min:"10",max:"500",step:"10",value:a.maxHistoryItems,onChange:d=>t(c=>({...c,maxHistoryItems:parseInt(d.target.value)})),className:"w-full h-2 bg-adobe-bg-secondary rounded-lg appearance-none cursor-pointer"})]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"Advanced"}),e.jsx("div",{className:"space-y-3",children:e.jsxs("label",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"checkbox",checked:a.debugMode,onChange:d=>t(c=>({...c,debugMode:d.target.checked})),className:"w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"}),e.jsx("span",{className:"text-sm text-adobe-text-primary",children:"Debug mode"})]})})]}),e.jsx("div",{className:"pt-4",children:e.jsxs("button",{onClick:h,disabled:i,className:"flex items-center gap-2 px-6 py-2 bg-adobe-accent text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-adobe-accent/90 transition-colors",children:[e.jsx(Q,{size:16}),e.jsx("span",{children:i?"Saving...":"Save Settings"})]})})]}):s==="analytics"?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:"Analytics"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:g,onChange:d=>y(d.target.value),className:"appearance-none bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-1.5 pr-8 text-sm text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none cursor-pointer",children:[e.jsx("option",{value:"7d",children:"Last 7 days"}),e.jsx("option",{value:"30d",children:"Last 30 days"}),e.jsx("option",{value:"90d",children:"Last 90 days"}),e.jsx("option",{value:"all",children:"All time"})]}),e.jsx(z,{size:14,className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary pointer-events-none"})]}),e.jsx("button",{onClick:S,disabled:p,className:"p-1.5 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(ae,{size:16,className:p?"animate-spin":""})})]})]}),p?e.jsx("div",{className:"grid grid-cols-2 gap-4",children:[...Array(4)].map((d,c)=>e.jsx("div",{className:"h-24 bg-adobe-bg-secondary rounded-md animate-pulse"},c))}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-1",children:"Messages"}),e.jsx("div",{className:"text-2xl font-medium text-adobe-text-primary",children:j.messages.toLocaleString()})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-1",children:"Sessions"}),e.jsx("div",{className:"text-2xl font-medium text-adobe-text-primary",children:j.sessions.toLocaleString()})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-1",children:"Tokens Used"}),e.jsx("div",{className:"text-2xl font-medium text-adobe-text-primary",children:j.tokens.toLocaleString()})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-1",children:"Est. Cost"}),e.jsxs("div",{className:"text-2xl font-medium text-adobe-text-primary",children:["$",j.cost.toFixed(4)]})]})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary",children:"Average Latency"}),e.jsxs("div",{className:"text-sm font-medium text-adobe-text-primary",children:[j.avgLatency.toFixed(2)," seconds"]})]}),e.jsx("div",{className:"h-2 bg-adobe-bg-tertiary rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-adobe-accent transition-all duration-300",style:{width:`${Math.min(100,j.avgLatency*50)}%`}})})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("h4",{className:"text-sm font-medium text-adobe-text-primary mb-2",children:"Performance Tips"}),e.jsxs("ul",{className:"text-sm text-adobe-text-secondary space-y-1",children:[e.jsx("li",{children:"• Use concise prompts to reduce token usage"}),e.jsx("li",{children:"• Select faster models for simple tasks"}),e.jsx("li",{children:"• Monitor usage to optimize costs"})]})]})]})]}):s==="help"?e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary mb-2",children:"Help & Support"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"p-4 bg-adobe-bg-secondary rounded-md",children:[e.jsx("h4",{className:"font-medium text-adobe-text-primary mb-2",children:"Documentation"}),e.jsx("p",{className:"text-sm text-adobe-text-secondary",children:"Read our comprehensive documentation for detailed guides."})]}),e.jsxs("div",{className:"p-4 bg-adobe-bg-secondary rounded-md",children:[e.jsx("h4",{className:"font-medium text-adobe-text-primary mb-2",children:"FAQ"}),e.jsx("p",{className:"text-sm text-adobe-text-secondary",children:"Find answers to frequently asked questions."})]}),e.jsxs("div",{className:"p-4 bg-adobe-bg-secondary rounded-md",children:[e.jsx("h4",{className:"font-medium text-adobe-text-primary mb-2",children:"Contact Support"}),e.jsx("p",{className:"text-sm text-adobe-text-secondary",children:"Email <NAME_EMAIL> for assistance."})]})]})]}):e.jsx("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Y,{size:48,className:"mx-auto mb-4 opacity-50"}),e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary mb-2",children:"About SahAI"}),e.jsx("p",{className:"text-sm mb-4",children:"Version 2.0.0"}),e.jsx("p",{className:"text-sm",children:"AI-powered assistant for Adobe Creative Suite"})]})})})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary border-t border-adobe-border p-3 text-xs text-adobe-text-secondary text-center",children:[s==="settings"&&"General Settings",s==="analytics"&&"Usage Analytics",s==="help"&&"Help & Support",s==="about"&&"About SahAI"]})]})})},et=()=>{const{closeModal:o}=P(),{sessions:n,isLoading:a,error:t,loadHistory:s,deleteSession:r,setCurrentSession:i,getSortedSessions:l}=q(),[u,x]=m.useState(""),[g,y]=m.useState(null),[p,v]=m.useState("recent");m.useEffect(()=>{s()},[s]);const N=l().filter(h=>h.title.toLowerCase().includes(u.toLowerCase())||h.messages.some(d=>d.content.toLowerCase().includes(u.toLowerCase()))).sort((h,d)=>p==="alphabetical"?h.title.localeCompare(d.title):p==="oldest"?h.createdAt-d.createdAt:d.createdAt-h.createdAt),L=async(h,d)=>{d.stopPropagation(),confirm("Are you sure you want to delete this chat session?")&&await r(h)},j=h=>{const d=new Date(h),f=(new Date().getTime()-d.getTime())/(1e3*60*60);return f<24?d.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):f<24*7?d.toLocaleDateString([],{weekday:"short",hour:"2-digit",minute:"2-digit"}):d.toLocaleDateString([],{month:"short",day:"numeric",year:"numeric"})},S=h=>{const d=h.messages[h.messages.length-1];if(!d)return"No messages";const c=d.content.slice(0,100);return c.length<d.content.length?`${c}...`:c};return e.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:e.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col",children:[e.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"Chat History"}),e.jsx("button",{onClick:o,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(T,{size:20})})]})}),e.jsx("div",{className:"p-4 border-b border-adobe-border",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx("input",{type:"text",value:u,onChange:h=>x(h.target.value),placeholder:"Search chat history...",className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pr-10 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none text-sm"}),e.jsx("button",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary hover:text-adobe-text-primary",onClick:()=>x(""),children:u?e.jsx(T,{size:16}):e.jsx(B,{size:16})})]}),e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:p,onChange:h=>v(h.target.value),className:"appearance-none bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pl-3 pr-8 text-adobe-text-primary text-sm focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none cursor-pointer",children:[e.jsx("option",{value:"recent",children:"Most Recent"}),e.jsx("option",{value:"oldest",children:"Oldest First"}),e.jsx("option",{value:"alphabetical",children:"Alphabetical"})]}),e.jsx(z,{size:16,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary pointer-events-none"})]})]})}),e.jsx("div",{className:"flex-1 overflow-hidden",children:a?e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-adobe-accent"})}):t?e.jsxs("div",{className:"p-4 text-center",children:[e.jsxs("div",{className:"bg-red-900/20 border border-red-800/50 rounded-lg p-4 mb-4",children:[e.jsx("p",{className:"text-red-400 font-medium mb-2",children:"Error loading history:"}),e.jsx("p",{className:"text-sm text-red-300",children:t})]}),e.jsx("button",{onClick:s,className:"px-4 py-2 bg-adobe-accent hover:bg-adobe-accent-hover text-white rounded-md transition-colors",children:"Retry Loading History"})]}):N.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary gap-2",children:[e.jsx(W,{size:48,className:"opacity-50"}),e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:u?"No matching sessions found":"No chat history yet"}),e.jsx("p",{className:"text-sm",children:u?"Try a different search term":"Start a new conversation to see it here"})]}):e.jsx("div",{className:"h-full overflow-y-auto p-2 space-y-2",children:N.map(h=>e.jsxs("div",{className:`p-3 rounded-md cursor-pointer transition-colors ${(g==null?void 0:g.id)===h.id?"bg-adobe-accent/10 border-l-2 border-adobe-accent":"bg-adobe-bg-secondary hover:bg-adobe-bg-tertiary"}`,onClick:()=>y(h),children:[e.jsxs("div",{className:"flex justify-between items-start gap-2",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"font-medium text-adobe-text-primary truncate",children:h.title}),e.jsx("p",{className:"text-sm text-adobe-text-secondary mt-1 line-clamp-2",children:S(h)})]}),e.jsx("button",{onClick:d=>L(h.id,d),className:"text-adobe-text-secondary hover:text-adobe-error transition-colors p-1",title:"Delete session",children:e.jsx(Ee,{size:14})})]}),e.jsxs("div",{className:"flex justify-between items-center mt-2",children:[e.jsxs("div",{className:"flex items-center gap-1 text-xs text-adobe-text-secondary",children:[e.jsx(W,{size:12}),e.jsxs("span",{children:[h.messages.length," messages"]})]}),e.jsxs("div",{className:"flex items-center gap-1 text-xs text-adobe-text-secondary",children:[e.jsx(Me,{size:12}),e.jsx("span",{children:j(h.createdAt)})]})]})]},h.id))})}),e.jsxs("div",{className:"p-3 border-t border-adobe-border text-xs text-adobe-text-secondary text-center",children:["Showing ",N.length," of ",n.length," chat sessions"]})]})})},tt=()=>{const{closeModal:o}=P(),{getActiveProvider:n,providers:a}=A(),[t,s]=m.useState({isOnline:null,isChecking:!1}),r=n(),i=async(y=!1)=>{if(!(r!=null&&r.isConfigured)){s({isOnline:null,isChecking:!1});return}s(p=>({...p,isChecking:!0,error:void 0}));try{const p=await K.checkProviderStatus(r.id,{apiKey:r.apiKey,baseURL:r.baseURL});s({isOnline:p.isOnline,latency:p.latency,isChecking:!1,lastChecked:Date.now()})}catch(p){s({isOnline:!1,isChecking:!1,error:p.message,lastChecked:Date.now()})}};m.useEffect(()=>{i();const y=setInterval(i,3e4);return()=>clearInterval(y)},[r]);const l=()=>t.isChecking?e.jsx(O,{size:20,className:"animate-spin text-yellow-500"}):t.isOnline===!0?e.jsx(Pe,{size:20,className:"text-green-500"}):t.isOnline===!1?e.jsx(Ie,{size:20,className:"text-red-500"}):e.jsx(X,{size:20,className:"text-gray-500"}),u=()=>t.isChecking?"Checking connection...":t.isOnline===!0?"Online":t.isOnline===!1?"Offline":"Unknown",x=()=>t.isChecking?"text-yellow-600":t.isOnline===!0?"text-green-600":t.isOnline===!1?"text-red-600":"text-gray-600",g=()=>t.isOnline===!0?"good":t.isOnline===!1?"critical":"warning";return e.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:e.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col",children:[e.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"Provider Status"}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("button",{onClick:()=>i(!0),disabled:t.isChecking,className:"p-1 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded transition-colors disabled:opacity-50",title:"Refresh status",children:e.jsx(ae,{size:18,className:t.isChecking?"animate-spin":""})}),e.jsx("button",{onClick:o,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(T,{size:20})})]})]})}),e.jsx("div",{className:"flex-1 overflow-hidden p-4",children:r?e.jsxs("div",{className:"h-full flex flex-col gap-4",children:[e.jsx("div",{className:"bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex-shrink-0",children:l()}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-adobe-text-primary",children:r.name}),e.jsx("p",{className:`text-sm font-medium ${x()}`,children:u()})]})]}),e.jsx("div",{className:`text-xs px-2 py-1 rounded ${g()==="good"?"bg-green-900/30 text-green-500":g()==="warning"?"bg-yellow-900/30 text-yellow-500":"bg-red-900/30 text-red-500"}`,children:u()})]})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-adobe-text-secondary",children:"Latency"}),e.jsx("span",{className:"text-xs text-adobe-text-tertiary",children:"Lower is better"})]}),e.jsx("div",{className:"mt-2",children:t.latency?e.jsxs("div",{className:"flex items-end gap-2",children:[e.jsxs("span",{className:"text-2xl font-medium text-adobe-text-primary",children:[t.latency,"ms"]}),e.jsx("span",{className:`text-xs mb-1 ${t.latency<100?"text-green-500":t.latency<300?"text-yellow-500":"text-red-500"}`,children:t.latency<100?"Excellent":t.latency<300?"Good":"Poor"})]}):e.jsx("span",{className:"text-adobe-text-tertiary",children:"--"})})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary",children:"Last Checked"}),e.jsx("div",{className:"mt-2",children:t.lastChecked?e.jsxs("div",{className:"text-adobe-text-primary",children:[e.jsx("div",{className:"text-xl font-medium",children:new Date(t.lastChecked).toLocaleTimeString()}),e.jsx("div",{className:"text-xs text-adobe-text-tertiary mt-1",children:new Date(t.lastChecked).toLocaleDateString()})]}):e.jsx("span",{className:"text-adobe-text-tertiary",children:"--"})})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border col-span-2",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary",children:"Endpoint"}),e.jsx("div",{className:"mt-2",children:r.baseURL?e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"truncate text-adobe-text-primary font-mono text-sm",children:r.baseURL}),e.jsx("button",{className:"text-xs text-adobe-accent hover:text-adobe-accent-hover",onClick:()=>navigator.clipboard.writeText(r.baseURL||""),children:"Copy"})]}):e.jsx("span",{className:"text-adobe-text-tertiary",children:"Not configured"})})]})]}),e.jsxs("div",{className:"flex-1 bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border overflow-auto",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-2",children:"Status Details"}),t.error?e.jsxs("div",{className:"p-3 bg-red-900/20 border border-red-800/50 rounded text-sm text-red-400",children:[e.jsx("div",{className:"font-medium mb-1",children:"Error:"}),e.jsx("div",{children:t.error})]}):e.jsx("div",{className:"text-sm text-adobe-text-primary",children:t.isChecking?"Checking provider status...":t.isOnline===!0?"Provider is online and responding normally.":t.isOnline===!1?"Provider is offline or not responding to requests.":"Provider status unknown. Please check configuration."})]})]}):e.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary gap-2",children:[e.jsx(X,{size:48,className:"opacity-50"}),e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:"No provider selected"}),e.jsx("p",{className:"text-sm",children:"Select a provider to check connection status"})]})}),e.jsx("div",{className:"p-3 border-t border-adobe-border text-xs text-adobe-text-secondary text-center bg-adobe-bg-secondary",children:"Status checks are performed automatically every 30 seconds"})]})})},st=()=>{const{modal:o}=P();if(!o)return null;switch(o){case"provider":return e.jsx(Xe,{});case"settings":return e.jsx(Ze,{});case"chat-history":return e.jsx(et,{});case"status":return e.jsx(tt,{});default:return null}},at=()=>e.jsxs("div",{className:"flex flex-col h-screen bg-adobe-bg text-adobe-text font-sans",children:[e.jsx(Fe,{}),e.jsx(Qe,{}),e.jsx(Ye,{}),e.jsx(st,{})]});ne();A.getState().loadSettings();G.createRoot(document.getElementById("root")).render(e.jsx(se.StrictMode,{children:e.jsx(at,{})}));
