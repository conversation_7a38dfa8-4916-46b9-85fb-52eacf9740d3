## 🔮 CEP-Ready “Cline Magic” Translation Plan  
> Keep the same **zero-hard-coding** model list as C<PERSON>, but inside an Adobe CEP extension.

---

### 1. 🗂️ File Map (drop-in replacements)

```
host/
├─ api-bridge.jsx          ← NEW  (Node-like HTTPS inside ExtendScript)
├─ index.jsx               ← UPDATED (entry point that registers api-bridge)

client/src/utils/
├─ cepIntegration.ts       ← UPDATED (talks to the new bridge)

client/src/stores/
├─ settingsStore.ts        ← UPDATED (uses the new bridge)

client/src/components/Modals/
├─ ProviderModal.tsx       ← NO CHANGE (already works with the store)
```

---

### 2. 🧩 Code – copy & paste

#### `host/api-bridge.jsx`
```javascript
/**
 * api-bridge.jsx
 * ExtendScript side: performs HTTPS GET /v1/models and returns JSON.
 * Requires Adobe CEP v9+ (CEP 11 ships with `XMLHttpRequest` polyfill).
 */
(function (global) {
  /**
   * listModels(providerId, baseURL, apiKey)
   * @returns Array<{id, name, description, context_length, is_recommended}>
   */
  function listModels(providerId, baseURL, apiKey) {
    var url = (baseURL || defaultBaseURL(providerId)) + '/v1/models';
    var xhr = new XMLHttpRequest();
    xhr.open('GET', url);
    xhr.setRequestHeader('Authorization', 'Bearer ' + apiKey);
    xhr.send();

    if (xhr.status !== 200)
      throw new Error('HTTP ' + xhr.status + ': ' + xhr.statusText);

    var json = JSON.parse(xhr.responseText);
    return (json.data || json).map(function (m) {
      return {
        id: m.id,
        name: m.name || m.id,
        description: m.description || '',
        context_length: m.context_length || m.max_tokens || 0,
        is_recommended: m.is_recommended || false
      };
    });
  }

  function defaultBaseURL(id) {
    var map = {
      openai:     'https://api.openai.com',
      anthropic:  'https://api.anthropic.com',
      gemini:     'https://generativelanguage.googleapis.com',
      groq:       'https://api.groq.com',
      deepseek:   'https://api.deepseek.com',
      mistral:    'https://api.mistral.ai',
      moonshot:   'https://api.moonshot.cn',
      openrouter: 'https://openrouter.ai/api',
      perplexity: 'https://api.perplexity.ai',
      qwen:       'https://dashscope.aliyuncs.com/api',
      together:   'https://api.together.xyz',
      vertex:     'https://us-central1-aiplatform.googleapis.com/v1/projects/YOUR_PROJECT/locations/us-central1',
      xai:        'https://api.x.ai',
      ollama:     'http://localhost:11434',
      lmstudio:   'http://localhost:1234'
    };
    return map[id] || '';
  }

  // CEP evalScript bridge registration
  global.apiBridge = { listModels };
})(this);
```

#### `host/index.jsx` (snippet – load the bridge)
```javascript
//@include "api-bridge.jsx"
```

---

#### `client/src/utils/cepIntegration.ts`
```typescript
/**
 * Thin wrapper that calls the ExtendScript bridge.
 */
export const ProviderBridge = {
  async listModels(
    providerId: string,
    baseURL?: string,
    apiKey?: string
  ): Promise<Array<{
    id: string;
    name: string;
    description?: string;
    contextLength?: number;
    isRecommended?: boolean;
  }>> {
    const script = `apiBridge.listModels('${providerId}', '${baseURL || ''}', '${apiKey || ''}')`;
    const res = await evalScriptAsync(script);
    return JSON.parse(res);
  }
};

/**
 * Promise wrapper around CSInterface.evalScript
 */
function evalScriptAsync(script: string): Promise<string> {
  const cs = new CSInterface();
  return new Promise((resolve, reject) => {
    cs.evalScript(script, (result) => {
      if (result === 'EvalScript error.') reject(new Error(result));
      else resolve(result);
    });
  });
}
```

---

#### `client/src/stores/settingsStore.ts` – PATCHES
Replace the single line inside `loadModelsForProvider`:

```typescript
// old
const { ProviderBridge } = await import('../../utils/cepIntegration');
// new (already imported)
const models = await ProviderBridge.listModels(
  providerId,
  provider.baseURL,
  provider.apiKey
);
```

No other changes needed.

---

### 3. ✅ Checklist to wire everything up

| Task | Script / Action |
|---|---|
| **Register JSX** | Add `@include "api-bridge.jsx"` to your host `index.jsx` |
| **Manifest** | Ensure `<ScriptPath>host/index.jsx</ScriptPath>` is declared |
| **Permissions** | Manifest must include `<CEFCommandLine>--allow-running-insecure-content --disable-web-security</CEFCommandLine>` if using `localhost` endpoints |
| **Rebuild** | `npm run build` or `vite build` → copy `dist/` into `client/` |
| **Install** | Double-click `install-extension.bat` (or `.sh`) to reload the panel |

---

### 4. 🔍 Test Flow (what you’ll see)

1. Open panel → click **Configure AI Provider**.  
2. Pick **Moonshot** → paste key.  
3. Spinner appears for ~1 s.  
4. Dropdown shows **Kimi-K2-Instruct** and **Kimi-K2-Base** – pulled live from the real API.

Repeat for any other provider → same behavior, zero edits.

---

### 5. 🎉 Done

Your CEP extension now behaves **identically** to Cline:  
- **No hard-coded model lists**  
- **Always up-to-date**  
- **Same React components** (`ProviderModal.tsx`)  

Copy the files above and enjoy the magic.