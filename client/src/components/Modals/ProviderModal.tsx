import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useSettingsStore } from '../stores/settingsStore';
import { useModalStore } from '../stores/modalStore';
import { Search, Loader2 } from 'lucide-react';
import { ProviderLogo } from '../ui/ProviderLogo';
import type { Model } from '../stores/settingsStore';

const providerOptions = [
  { value: 'openai', label: 'OpenAI' },
  { value: 'anthropic', label: 'Anthropic' },
  { value: 'gemini', label: 'Google Gemini' },
  { value: 'groq', label: 'Groq' },
  { value: 'deepseek', label: 'DeepSeek' },
  { value: 'mistral', label: 'Mistral' },
  { value: 'moonshot', label: 'Moonshot AI' },
  { value: 'openrouter', label: 'OpenRouter' },
  { value: 'perplexity', label: 'Perplexity' },
  { value: 'qwen', label: '<PERSON><PERSON><PERSON> Qwen' },
  { value: 'together', label: 'Together AI' },
  { value: 'vertex', label: 'Google Vertex AI' },
  { value: 'xai', label: 'xAI' },
  { value: 'ollama', label: 'Ollama' },
  { value: 'lmstudio', label: 'LM Studio' },
];

export const ProviderModal: React.FC = () => {
  const { closeModal } = useModalStore();
  const { providers, saveProviderSelection, loadModelsForProvider } = useSettingsStore();

  /* ---------- local state ---------- */
  const [selectedProvider, setSelectedProvider] = useState('');
  const [selectedModel, setSelectedModel] = useState('');
  const [apiKey, setApiKey] = useState('');

  const [providerSearch, setProviderSearch] = useState('');
  const [modelSearch, setModelSearch] = useState('');

  const [showProviderDropdown, setShowProviderDropdown] = useState(false);
  const [showModelDropdown, setShowModelDropdown] = useState(false);

  const providerRef = useRef<HTMLDivElement>(null);
  const modelRef = useRef<HTMLDivElement>(null);

  /* ---------- derived state ---------- */
  const providerState = providers.find(p => p.id === selectedProvider);
  const models = providerState?.models || [];

  const filteredProviders = providerOptions.filter(p =>
    p.label.toLowerCase().includes(providerSearch.toLowerCase())
  );

  const filteredModels = models.filter(m =>
    m.name.toLowerCase().includes(modelSearch.toLowerCase())
  );

  /* ---------- side effects ---------- */
  // Close dropdowns on outside click
  useEffect(() => {
    const handleOutside = (e: MouseEvent) => {
      if (providerRef.current && !providerRef.current.contains(e.target as Node))
        setShowProviderDropdown(false);
      if (modelRef.current && !modelRef.current.contains(e.target as Node))
        setShowModelDropdown(false);
    };
    document.addEventListener('mousedown', handleOutside);
    return () => document.removeEventListener('mousedown', handleOutside);
  }, []);

  // Auto-load models when we have both provider & key
  useEffect(() => {
    if (!selectedProvider || !apiKey.trim()) return;
    const timer = setTimeout(() => {
      saveProviderSelection(selectedProvider, { apiKey });
      loadModelsForProvider(selectedProvider);
    }, 300);
    return () => clearTimeout(timer);
  }, [selectedProvider, apiKey, saveProviderSelection, loadModelsForProvider]);

  /* ---------- handlers ---------- */
  const handleProviderSelect = useCallback(
    (id: string) => {
      const label = providerOptions.find(p => p.value === id)?.label || '';
      setSelectedProvider(id);
      setProviderSearch(label);
      setSelectedModel('');
      setModelSearch('');
      setShowProviderDropdown(false);
    },
    []
  );

  const handleModelSelect = useCallback(
    (id: string) => {
      const name = models.find(m => m.id === id)?.name || '';
      setSelectedModel(id);
      setModelSearch(name);
      setShowModelDropdown(false);
    },
    [models]
  );

  const handleSave = useCallback(() => {
    if (!selectedProvider || !selectedModel || !apiKey.trim()) return;
    saveProviderSelection(selectedProvider, {
      apiKey,
      selectedModelId: selectedModel,
    });
    closeModal();
  }, [selectedProvider, selectedModel, apiKey, saveProviderSelection, closeModal]);

  /* ---------- render ---------- */
  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm">
      <div className="bg-adobe-bg-primary border border-adobe-border rounded-lg w-[500px] shadow-2xl relative">
        {/* Header */}
        <div className="bg-adobe-bg-secondary border-b border-adobe-border p-4 flex items-center justify-between">
          <h2 className="text-lg font-semibold text-adobe-text-primary">
            AI Provider Configuration
          </h2>
          <button
            onClick={closeModal}
            className="text-adobe-text-secondary hover:text-adobe-text-primary transition-colors"
          >
            ✕
          </button>
        </div>

        {/* Body */}
        <div className="p-6 space-y-6 overflow-visible">
          {/* Provider */}
          <div className="relative" ref={providerRef}>
            <label className="block text-sm font-medium text-adobe-text-primary mb-2">
              Provider
            </label>
            <div className="relative">
              <input
                type="text"
                value={providerSearch}
                onChange={e => {
                  setProviderSearch(e.target.value);
                  setShowProviderDropdown(true);
                }}
                onFocus={() => setShowProviderDropdown(true)}
                placeholder="Search providers..."
                className="w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none pr-10"
              />
              <Search className="absolute right-3 top-1/2 -translate-y-1/2 text-adobe-text-secondary" size={18} />
            </div>

            {showProviderDropdown && (
              <div className="absolute z-50 mt-2 w-full bg-adobe-bg-secondary border border-adobe-border rounded-md shadow-2xl max-h-60 overflow-auto">
                {filteredProviders.map(p => (
                  <div
                    key={p.value}
                    className="px-4 py-3 cursor-pointer flex items-center space-x-3 hover:bg-adobe-bg-tertiary"
                    onClick={() => handleProviderSelect(p.value)}
                  >
                    <ProviderLogo provider={p.value} size={16} />
                    <span className="text-adobe-text-primary font-medium">{p.label}</span>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Model */}
          <div className="relative" ref={modelRef}>
            <label className="block text-sm font-medium text-adobe-text-primary mb-2">
              Model
            </label>
            <div className="relative">
              <input
                type="text"
                value={modelSearch}
                onChange={e => {
                  setModelSearch(e.target.value);
                  setShowModelDropdown(true);
                }}
                onFocus={() => selectedProvider && setShowModelDropdown(true)}
                placeholder={selectedProvider ? 'Search models...' : 'Select a provider first'}
                disabled={!selectedProvider}
                className="w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none pr-10 disabled:opacity-50"
              />
              <Search className="absolute right-3 top-1/2 -translate-y-1/2 text-adobe-text-secondary" size={18} />
              {providerState?.isLoading && (
                <Loader2 className="absolute right-10 top-1/2 -translate-y-1/2 text-adobe-text-secondary animate-spin" size={16} />
              )}
            </div>

            {showModelDropdown && selectedProvider && (
              <div className="absolute z-50 mt-2 w-full bg-adobe-bg-secondary border border-adobe-border rounded-md shadow-2xl max-h-60 overflow-auto">
                {providerState?.isLoading && models.length === 0 && (
                  <div className="px-4 py-3 flex items-center space-x-2 text-adobe-text-secondary">
                    <Loader2 size={16} className="animate-spin" />
                    <span>Loading models...</span>
                  </div>
                )}

                {providerState?.error && models.length === 0 && (
                  <div className="px-4 py-3">
                    <div className="text-red-400 text-sm font-medium">Failed to load models</div>
                    <div className="text-adobe-text-secondary text-xs mt-1">{providerState.error}</div>
                    <button
                      onClick={() => loadModelsForProvider(selectedProvider)}
                      className="text-adobe-accent text-xs mt-2 hover:underline"
                    >
                      Retry
                    </button>
                  </div>
                )}

                {filteredModels.map(m => (
                  <div
                    key={m.id}
                    className="px-4 py-3 cursor-pointer hover:bg-adobe-bg-tertiary"
                    onClick={() => handleModelSelect(m.id)}
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-adobe-text-primary font-medium">{m.name}</span>
                      {m.isRecommended && (
                        <span className="text-xs bg-adobe-accent/20 text-adobe-accent px-2 py-1 rounded">
                          Recommended
                        </span>
                      )}
                    </div>
                    {m.description && (
                      <p className="text-xs text-adobe-text-secondary mt-1">{m.description}</p>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* API Key */}
          <div>
            <label className="block text-sm font-medium text-adobe-text-primary mb-2">
              API Key
            </label>
            <input
              type="password"
              value={apiKey}
              onChange={e => setApiKey(e.target.value)}
              placeholder="Enter your API key"
              className="w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none"
            />
          </div>
        </div>

        {/* Footer */}
        <div className="bg-adobe-bg-secondary border-t border-adobe-border p-4 flex justify-between">
          <button
            onClick={closeModal}
            className="px-4 py-2 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={!selectedProvider || !selectedModel || !apiKey.trim()}
            className="px-6 py-2 bg-adobe-accent text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-adobe-accent/90 transition-colors"
          >
            Save & Close
          </button>
        </div>
      </div>
    </div>
  );
};